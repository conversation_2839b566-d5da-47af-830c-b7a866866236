import AdminLayout from '@/Layouts/AdminLayout'
import React from 'react'
import { useState } from 'react';
import DataTable from 'react-data-table-component'
import { customStyles } from './components/customStyles';
import { IoMdRefresh } from "react-icons/io";
import { FaSearch } from 'react-icons/fa';
import { FaRegEdit } from "react-icons/fa";
import SetDomainStatusPopup from './components/SetDomainStatusPopup';
import { MdOutlineFilterAlt } from 'react-icons/md';
import Filter from '@/Components/MarketPlace/Filter';
import SearchInput from '@/Components/Util/SearchInput';
import { router } from '@inertiajs/react';
import { getEventValue } from '@/Util/TargetInputEvent';
import { useEffect } from 'react';

export default function MarketPlaceDomains(props) {

    const [page, setPage] = useState(1);
    const [row, setRow] = useState([]);
    const [modal, setModal] = useState(false);
    const [perPage, setperPage] = useState(10);
    const [totalRows, settotalRows] = useState(0);
    const [domains, setDomains] = useState(props.data);
    const [searchTerm, setSearchTerm] = useState(route().params.search);    
    const [tempDomains, setTempDomains] = useState(domains);
    const [pageLimit, setPageLimit] = useState(route().params.limit || 20);

    const getStatus = (status) => {
        let color = 'bg-primary';

        if(status.toLowerCase() == 'pending') color = `bg-yellow-500`
        else if(status.toLowerCase() == 'completed') color = `bg-green-500`
        else if(status.toLowerCase() == 'cancelled') color = `bg-red-500`

        return <div className='flex'>
            <span className={`w-2 h-2 mt-1 mr-2 rounded-full ${color}`}> </span>
            <span className='capitalize'>{status}</span>
        </div>
    }

    const handlePageChange = async (npage) => {
        setPage(npage)
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        setperPage(newPerPage)
    };

    const handlePopUp = (row) => {
        setRow(row)
        setModal(true)
    }

    const getAction = (row) => {
        return <div className='flex gap-1 font-bold'>
            <div className='has-tooltip'>
                <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-primary px-1 -mt-8'>View History</span>
                <button onClick={() => { }} className='bg-primary rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                    <FaSearch className=' font-bold' />
                </button>
            </div>
            <div className='has-tooltip'>
                <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-green-600 px-1 -mt-8'>Change Status</span>
                <button onClick={() => { handlePopUp(row) }} className='bg-green-700 rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                    <FaRegEdit className=' font-bold' />
                </button>
            </div> 
        </div>
    }

    const columns = [
        {
            id: 'User',
            name: 'User',
            selector: row => row.name,
            cell: row => <div className='capitalize'>{`${row.first_name} ${row.last_name}`}</div>,
            sortable: true,
            // width: '150px'
        },
        {
            id: 'Domain',
            name: 'Domain',
            selector: row => row.domain,
            cell: row => <div className='lowercase'>{row.domain}</div>,
            sortable: true,
            // width: '150px'
        },
        {
            id: 'Status',
            name: 'Status',
            selector: row => row.status,
            cell: row => getStatus(row.status),
            sortable: true,
            // width: '170px'
        },
        {
            id: 'Price',
            name: 'Price',
            selector: row => parseInt(row.price),
            cell: row => `$${parseInt(row.price)}`,
            sortable: true,
            width: '125px',
        },
        {
            id: "Actions",
            name: 'Actions',
            selector: row => row.id,
            cell: row => getAction(row),
            width: '110px',
        },
    ];

    const handleSearchChange = (searchParams) => {
        let payload = route().params;
        payload.search = (searchParams.search != "") ? searchParams.search : undefined;

        router.get(
            route('domains'),
            payload,
            { preserveState: true }
        );
    };

    const handleLimitChange = (e) => {
        setPageLimit(parseInt(getEventValue(e)));
    }

    useEffect(() => {
    setTempDomains(props.data)
    }, [props.data])
    

    return (
        <AdminLayout>
            <SetDomainStatusPopup
                row={row}
                modal={modal}
                showModal={setModal}
                tempDomains={tempDomains}
                setTempDomains={setTempDomains}
            ></SetDomainStatusPopup>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col px-5 rounded-lg ">
                <div className='flex justify-start'>
                    <div>
                        <div className='text-3xl font-semibold mb-3'>
                            Marketplace Domains
                        </div>
                        <span className='text-gray-500 max-w-lg'>
                            View and Manage Marketplace Domains
                        </span>
                    </div>
                </div>
                <div
                    className="flex justify-start"
                    style={{ position: "relative", top: "20px"}}
                >
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={pageLimit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="flex justify-between items-center mt-4 pt-4">
                    <div className="flex items-center">
                        <label className="flex items-center">
                            <MdOutlineFilterAlt />
                            <span className="ml-2 text-sm text-gray-600">
                                Filter:
                            </span>
                        </label>
                        <Filter />
                    </div>

                    <SearchInput
                        onSearchChange={handleSearchChange}
                        placeholder="Search domain"
                    />

                </div>

                <DataTable
                    key={pageLimit}
                    columns={columns}
                    data={tempDomains}
                    pagination
                    persistTableHead
                    highlightOnHover
                    customStyles={customStyles}
                    pointerOnHover
                    selectableRows
                    fixedHeader
                    // paginationServer
                    paginationTotalRows={totalRows}
                    paginationDefaultPage={page}
                    onChangeRowsPerPage={handlePerRowsChange}
                    onChangePage={handlePageChange}
                    style={{minHeight: '200vh'}}
                    fixedHeaderScrollHeight="100vh"
                    paginationPerPage={pageLimit}
                    paginationRowsPerPageOptions={[20, 25, 30, 40, 50, 100]}
                />
            </div>
        </AdminLayout>
    )
}
