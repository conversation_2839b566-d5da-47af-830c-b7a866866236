<?php

namespace App\Modules\BankTransfer\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BankTransferListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'showItems' => 
            [
                'nullable', 
                'integer'
            ],
            'orderBy'   =>
            [
                'nullable',
                'string',
                Rule::in(
                    [
                        "referenceNumber:desc",
                        "referenceNumber:asc",
                        "name:desc",
                        "name:asc",
                        "company:desc",
                        "company:asc",
                        "amount:desc",
                        "amount:asc",
                        "createdAt:desc",
                        "createdAt:asc",
                    ]
                )
            ],
            'purpose'   =>
            [
                'nullable',
                'string',
                Rule::in(
                    [
                        'addCredit',
                        'offerPayment',
                        'marketPlacePayment'
                    ]
                )
            ],
            'referenceNumber' => ['nullable', 'string'],
            'accountName'     => ['nullable', 'string'],
            'company'         => ['nullable', 'string'],
        ];
    }
}
