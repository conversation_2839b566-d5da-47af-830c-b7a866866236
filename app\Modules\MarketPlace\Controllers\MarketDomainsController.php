<?php

namespace App\Modules\MarketPlace\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\MarketPlace\Requests\MarketPlaceCommissionRequest;
use App\Modules\MarketPlace\Requests\MarketPlaceDomainRequest;
use App\Modules\MarketPlace\Requests\UpdateDomainStatusRequest;
use App\Modules\MarketPlace\Services\MarketAuditService;
use App\Modules\MarketPlace\Services\MarketPlaceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class MarketDomainsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(MarketPlaceDomainRequest $request) : \Inertia\Response
    {
        return Inertia::render('MarketPlace/MarketPlaceDomains', ['data' => MarketPlaceService::instance()->getDomains($request)]);
    }

    public function commissions(MarketPlaceCommissionRequest $request) : \Inertia\Response
    {
        return Inertia::render('MarketPlace/MarketPlaceCommissions', ['data' => MarketAuditService::instance()->getCommissions($request)]);
    }

    public function update(UpdateDomainStatusRequest $request) : void
    {
        $request->update();
    }
}
