<?php

namespace App\Modules\BillingClient\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\BillingClient\Constants\PaymentSummaryType;
use App\Modules\BillingClient\Contracts\PaymentSummaryPickerInterface;
use App\Modules\BillingClient\Requests\ShowListRequest;
use App\Traits\CursorPaginate;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class BillingClientService
{
    use CursorPaginate;

    private $defaultpageLimit = 20;

    public static function instance(): self
    {
        $billingClientService = new self;

        return $billingClientService;
    }

    public function getIndex(ShowListRequest $request)
    {
        return $this->getSummaryAllList($request);
    }

    public function getPaymentSummaryById(int $summaryId)
    {
        $summary = $this->getQuery()
            ->where('payment_summaries.id', $summaryId)
            ->first();

        if (! $summary) {
            throw new FailedRequestException(404, 'Page not found.', '');
        }

        return $summary;
    }

    public function getView(Request $request)
    {
        $summary = $this->getQuery()
            ->where('payment_summaries.id', $request->id)
            ->select(
                'payment_summaries.*',
                'users.first_name',
                'users.last_name',
                'users.email',
            )
            ->get()
            ->first();

        if (! $summary) {
            throw new FailedRequestException(404, 'Page not found.', '');
        }

        switch ($summary->type) {
            case PaymentSummaryType::PAYMENT_INVOICE:
                return $this->getPaymentView($summary->payment_invoice_id, $summary->user_id, $summary);
            case PaymentSummaryType::PAYMENT_REIMBURSEMENT:
                return $this->getRefundView($summary->payment_invoice_id, $summary->user_id, $summary);
            case PaymentSummaryType::MARKETPLACE_INVOICE:
                return $this->getPaymentView($summary->payment_market_place_invoice_id, $summary->user_id, $summary);
            case PaymentSummaryType::MARKETPLACE_REIMBURSEMENT:
                return $this->getRefundView($summary->payment_market_place_invoice_id, $summary->user_id, $summary);
            case PaymentSummaryType::ACCOUNT_BALANCE:
                return $this->getPaymentView($summary->payment_service_id, $summary->user_id, $summary);
            case PaymentSummaryType::MULTI_CHECKOUT_INVOICE:
                return $this->getPaymentView($summary->id, $summary->user_id, $summary);
            default:
                return $this->getPaymentView($summary->id, $summary->user_id, $summary);
        }
    }

    // PRIVATE FUNCTIONS
    private function getQuery()
    {
        return DB::client()->table('payment_summaries')
            ->join('users', 'users.id', '=', 'payment_summaries.user_id')
            ->whereNull('users.deleted_at');
    }

    private function getSummaryAllList(ShowListRequest $request)
    {
        // DB::enableQueryLog();
        $builder = $this->getQuery();
        $this->whenHasOrderby($builder, $request);
        $this->whenHasType($builder, $request);
        $this->whenHasEmail($builder, $request);
        $this->whenHasName($builder, $request);
        $pageLimit = $request->input('limit', $this->defaultpageLimit);
        $builder = $builder
            ->select(
                'payment_summaries.*',
                'users.first_name',
                'users.last_name',
                'users.email',
            )
            ->paginate($pageLimit)->withQueryString();
        // dd(DB::getQueryLog());

        $others = [
            'summary_types' => PaymentSummaryType::TEXT,
        ];

        return CursorPaginate::cursor($builder, $this->paramToURI($request), $others);
    }

    private function whenHasOrderby(Builder &$builder, ShowListRequest $request)
    {
        $builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            $orderby = explode(':', $request->orderby);

            $orderby[1]=trim($orderby[1]);

            if (count($orderby) == 2 && in_array($orderby[1], ['Asc', 'Desc'])) {
                switch ($orderby[0]) {
                    case 'Date':
                        $query->orderBy('payment_summaries.created_at', $orderby[1]);
                        break;
                    case 'Type':
                        $query->orderBy('payment_summaries.type', $orderby[1]);
                        break;
                    case 'Email':
                        $query->orderBy('users.email', $orderby[1]);
                    case 'Name':
                        $query->orderBy('users.last_name', $orderby[1]);
                        break;
                    default:
                        $query->orderBy('payment_summaries.id', 'desc');
                }
            } else {
                $query->orderBy('payment_summaries.id', 'desc');
            }
        })
            ->when(! $request->has('orderby'), function (Builder $query) {
                $query->orderBy('payment_summaries.id', 'desc');
            });
    }

    private static function whenHasEmail(&$builder, $request)
    {
        $builder->when($request->has('email'), function (Builder $query) use ($request) {
            $email = $request->email;
            $query->where('users.email', 'ilike', $email.'%');
        });
    }

    private static function whenHasName(&$builder, $request)
    {
        $builder->when($request->has('name'), function (Builder $query) use ($request) {
            $name = $request->name;
            $query->where('users.first_name', 'ilike', $name.'%');
            $query->orWhere('users.last_name', 'ilike', $name.'%');
        });
    }

    private static function whenHasType(&$builder, $request)
    {
        $builder->when($request->has('type'), function (Builder $query) use ($request) {
            $type = array_search($request->type, PaymentSummaryType::TEXT);
            if (! $type || ! in_array($type, PaymentSummaryType::ALL)) {
                return;
            }
            $query->where('payment_summaries.type', '=', $type);
        });
    }

    private function paramToURI(ShowListRequest $request): array
    {
        $param = [];

        if ($request->has('orderby')) {
            $param[] = 'orderby='.$request->orderby;
        }

        if ($request->has('name')) {
            $param[] = 'name='.$request->name;
        }

        if ($request->has('email')) {
            $param[] = 'email='.$request->email;
        }

        if ($request->has('type')) {
            $param[] = 'type='.$request->type;
        }

        return $param;
    }

    private function getPaymentView(int $id, int $userId, object $summary)
    {
        $paymentProcessor = app(PaymentSummaryPickerInterface::class)->getType($summary->type);
        $data = $paymentProcessor->getPaymentbyId($id, $userId);
        $data['summaryData'] = $summary;

        return Inertia::render('Billing/Client/ShowPayment', $data);
    }

    private function getRefundView(int $id, int $userId, object $summary)
    {
        $paymentProcessor = app(PaymentSummaryPickerInterface::class)->getType($summary->type);
        $data = $paymentProcessor->getRefundbyId($id, $userId);
        $data['summaryData'] = $summary;

        return Inertia::render('Billing/Client/ShowRefund', ['item' => $data]);
    }
}
