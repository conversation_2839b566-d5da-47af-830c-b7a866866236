<?php

namespace App\Modules\MarketPlace\Requests;

use App\Events\EmailSentEvent;
use App\Mail\MailConstant;
use App\Modules\Client\Services\ClientNotificationService;
use App\Modules\MarketPlace\Constants\AfternicOfferConstants;
use App\Modules\MarketPlace\Mail\OfferUpdateMail;
use App\Util\Constant\QueueConnection;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\ValidationException;

class OfferUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules() : array
    {
        return [
            'id' => 'required|integer',
            'status' => 'required|string',
            'counter_offer' => 'required|integer',
            'feedback' => 'nullable|string|max:255'
        ];
    }

    public function update() : void
    {
        $date = Carbon::now();

        DB::table('public.afternic_offers')->where('id', $this->id)->update([
            'offer_status' => $this->status,
            'counter_offer_price' => $this->counter_offer,
            'updated_at' => $date,
        ]);

        DB::table('public.afternic_offer_histories')->insert([
            'afternic_offer_id' => $this->id,
            'offer_price' => $this->offer,
            'counter_offer_price' => $this->counter_offer,
            'offer_status' => $this->status,
            'feedback' => $this->feedback ? $this->feedback : '',
            'created_at' => $date,
            'updated_at' => $date,
        ]);

        $this->sendNotif();
    }

    private function sendNotif() {
        $data = DB::table('public.afternic_offers')->where('id', $this->id)->first();

        $status = '';
        if($data->offer_status == AfternicOfferConstants::COUNTER || $data->offer_status == AfternicOfferConstants::ACCEPTED)  {
            $status = str_replace('_', ' ', str_replace('offer', '', $data->offer_status)) . ' with price of $' . $data->counter_offer_price;
        } else $status = str_replace('_', ' ', str_replace('offer', '', $data->offer_status));

        (new ClientNotificationService())->createClientNotification(
            $data->user_id,
            'Offer Domain Update',
            'The admin has updated "' . $data->domain_name . '" offer status to ' . $status . '.',
            'important',
            redirectUrl: "/offers?domain=" . $data->domain_name
        );

        $this->sendEmailNotif($data->domain_name, $data->user_id, $data->offer_status);
    }

    private function sendEmailNotif($domain, $user_id, $status) {
        $status = ucwords(str_replace("_", " ", $status));

        $email = DB::table('users')->where('id', $user_id)->first()->email;

        $queueMessage = (new OfferUpdateMail([
            'domain' => $domain,
            'status' => $status,
        ]))
        ->onConnection(QueueConnection::EMAIL)
        ->onQueue(MailConstant::DOMAIN_OFFER);

        Mail::to($email)->send($queueMessage);

        event(new EmailSentEvent(
            Auth::id(),
            $domain,
            $email,
            'Domain Offer Update',
            'Domain Offer Update',
            json_encode(['domain' => $domain, 'status' => $status]),
            null
        ));
    }
}
