//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { ImSortAlphaAsc, ImSortAlphaDesc } from "react-icons/im";
import { MdOutlineSettings, MdOutlineFilterAlt } from "react-icons/md";
import { TbSortAscending2, TbSortDescending2 } from "react-icons/tb";

//* COMPONENTS
import CursorPaginate from "@/Components/Util/CursorPaginate";
import WireTransferItemComponent from "@/Components/WireTransfer/WireTransferItemComponent";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialWireTransferSectionAddCredit(
    {
        items,
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        itemCount = 0,
        total     = 0,
        SORT_TYPE, 
        paramOrderBy, 
        paramCompany, 
        selectedItems,
        setSelectedItems, 
        stateSelectedItem,
        setStateSelectedItem,
        setStateModalActiveNote
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    const columns = 
    [
        'referenceNumber', 
        'accountName', 
        'company', 
        'grossAmount', 
        'status',     
        'note', 
        'dateCreated', 
        'dateUpdated', 
        'actions'
    ]

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleSortOrder(sortOrder)
    {
        let payload = {};

        payload.orderby = sortOrder;

        router.get(route("billing.wire.transfer"), payload);
    };

    return (
        <div
            className='flex flex-col gap-4'
        >                
            <table className="min-w-[1200px] text-left border-spacing-y-2.5 border-separate">
                <thead className=" bg-gray-50 text-sm">
                    <tr>
                        <th className="">
                            <span>Reference No.</span>
                        </th>
                        <th className="py-3">
                            <label className="flex items-center space-x-2">
                                <span>Name</span>
                                <button
                                    onClick={() =>
                                        handleSortOrder(
                                            paramOrderBy ===
                                                SORT_TYPE.NAME_ASC
                                                ? SORT_TYPE.NAME_DESC
                                                : SORT_TYPE.NAME_ASC
                                        )
                                    }
                                    disabled={items.length === 0}
                                >
                                    {paramOrderBy ===
                                        SORT_TYPE.NAME_ASC ? (
                                        <ImSortAlphaAsc />
                                    ) : (
                                        <ImSortAlphaDesc />
                                    )}
                                </button>
                            </label>
                        </th>
                        <th className="">
                            <span>Company</span>
                        </th>
                        <th className="">
                            <span>Amount ($)</span>
                        </th>
                        {/* <th className="">
                            <span>Purpose</span>
                        </th> */}
                        <th className="">
                            <span>Status</span>
                        </th>
                        <th className="">
                            <span>Note</span>
                        </th>
                        <th className="">
                            <label className="flex items-center space-x-2">
                                <span>Date Created</span>
                                <button
                                    onClick={() =>
                                        handleSortOrder(
                                            paramOrderBy ===
                                                SORT_TYPE.CREATED_ASC
                                                ? SORT_TYPE.CREATED_DESC
                                                : SORT_TYPE.CREATED_ASC
                                        )
                                    }
                                    disabled={items.length === 0}
                                >
                                    {paramOrderBy ===
                                        SORT_TYPE.CREATED_ASC ? (
                                        <TbSortAscending2 />
                                    ) : (
                                        <TbSortDescending2 />
                                    )}
                                </button>
                            </label>
                        </th>
                        <th className="">
                            <label className="flex items-center space-x-2">
                                <span>Date Updated</span>
                                <button
                                    onClick={() =>
                                        handleSortOrder(
                                            paramOrderBy ===
                                                SORT_TYPE.UPDATED_ASC
                                                ? SORT_TYPE.UPDATED_DESC
                                                : SORT_TYPE.UPDATED_ASC
                                        )
                                    }
                                    disabled={items.length === 0}
                                >
                                    {paramOrderBy ===
                                        SORT_TYPE.UPDATED_ASC ? (
                                        <TbSortAscending2 />
                                    ) : (
                                        <TbSortDescending2 />
                                    )}
                                </button>
                            </label>
                        </th>
                        <th className="">
                            <span className="text-xl">
                                <MdOutlineSettings />
                            </span>
                        </th>
                    </tr>
                </thead>
                <tbody className="text-sm">
                    {
                        items.map(
                            (item, index) =>
                            (
                                <WireTransferItemComponent
                                    key={"ci-" + index}
                                    item={item}
                                    columns={columns}
                                    handleClickNote={
                                        () =>
                                        {                                            
                                            setStateSelectedItem(item);
                                            setStateModalActiveNote(true);
                                        }
                                    }
                                />
                            )
                        )
                    }
                </tbody>
            </table>

            {/* Pagination */}
            <CursorPaginate
                onFirstPage={onFirstPage}
                onLastPage={onLastPage}
                nextPageUrl={nextPageUrl}
                previousPageUrl={previousPageUrl}
                itemCount={itemCount}
                total={total}
                shouldPreserveState={true}
            />
        </div>
    );
}
