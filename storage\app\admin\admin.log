[2025-09-25 07:46:39] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 07:46:47] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 07:46:48] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 07:46:52] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:46:52] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:46:53] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:46:53] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:46:54] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:46:54] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:46:55] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:46:55] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:46:56] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:46:56] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:46:58] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:46:58] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:46:59] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:46:59] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:00] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:00] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:01] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Undefined array key "userId"  
[2025-09-25 07:47:01] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"userId\"","code":0}  
[2025-09-25 07:47:02] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:02] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:03] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:03] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:05] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:05] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:06] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:06] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:07] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:07] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:08] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:08] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:09] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:09] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:10] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:10] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:12] local.INFO: Domain deletion request email sent <NAME_EMAIL> for domain mahahawasaw.com  
[2025-09-25 07:47:12] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:12] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:14] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:14] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:15] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:15] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:16] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:16] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:17] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:17] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:18] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:18] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:19] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:19] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:20] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:20] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:22] local.INFO: Domain deletion request email sent <NAME_EMAIL> for domain lamtpeor.com  
[2025-09-25 07:47:23] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:23] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:24] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:24] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:25] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:25] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:26] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:26] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:27] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:27] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:28] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:28] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:29] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:29] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:30] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:31] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:32] local.INFO: Domain deletion request email sent <NAME_EMAIL> for domain cravytols.net  
[2025-09-25 07:47:33] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:33] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:34] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:34] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:35] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:35] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:36] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:36] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:37] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:37] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:47:39] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:47:39] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:48:44] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 07:50:56] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 07:50:58] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Undefined array key "userName"  
[2025-09-25 07:50:58] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Undefined array key "userName"  
[2025-09-25 07:50:59] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"userName\"","code":0}  
[2025-09-25 07:51:00] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:00] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:01] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:01] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:02] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:03] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:04] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:04] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:05] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:05] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:06] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:06] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:06] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:08] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:08] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:08] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:09] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Undefined array key "userId"  
[2025-09-25 07:51:09] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Undefined array key "userId"  
[2025-09-25 07:51:09] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"userId\"","code":0}  
[2025-09-25 07:51:10] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:10] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:10] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:11] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:11] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:11] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:12] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:13] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:13] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:14] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:14] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:14] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:15] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:15] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:15] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:16] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:16] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:16] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:17] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:17] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:17] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:19] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:19] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:19] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:20] local.INFO: Domain deletion request email sent <NAME_EMAIL> for domain cdforfree.com  
[2025-09-25 07:51:21] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:21] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:21] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:22] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:22] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:22] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:24] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:24] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:24] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:25] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:25] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:25] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:27] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:27] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:27] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:28] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:28] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:28] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:29] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:29] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:29] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:31] local.INFO: Domain deletion request email sent <NAME_EMAIL> for domain mahahawasaw.net  
[2025-09-25 07:51:32] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:32] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:32] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:33] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:33] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:33] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:34] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:34] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:34] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:35] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:35] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:35] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:36] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:37] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:37] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:38] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:38] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:38] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:39] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:39] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:39] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:41] local.INFO: Domain deletion request email sent <NAME_EMAIL> for domain ploguex.com  
[2025-09-25 07:51:42] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:42] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:42] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:43] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:43] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:43] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:44] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:44] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:44] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:46] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:46] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:46] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:47] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:47] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:47] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:48] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:48] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:48] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:49] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:49] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:49] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:51] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:51] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:51] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:52] local.INFO: Domain deletion request email sent <NAME_EMAIL> for domain cravytols.net  
[2025-09-25 07:51:53] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:53] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:53] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:51:54] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:54] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:51:54] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:52:12] local.INFO: Domain deletion request email sent <NAME_EMAIL> for domain pokitawork.net  
[2025-09-25 07:52:13] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:52:13] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:52:13] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:52:14] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:52:14] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:52:14] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:52:15] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:52:15] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:52:15] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:52:17] local.ERROR: Failed to send domain deletion request <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:52:17] local.ERROR: SendDomainDeleteRequestEmail job <NAME_EMAIL>: Expected response code "354" but got code "550", with message "550 5.7.0 Too many emails per second. Please upgrade your plan https://mailtrap.io/billing/plans/testing".  
[2025-09-25 07:52:17] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException","message":"Expected response code \"354\" but got code \"550\", with message \"550 5.7.0 Too many emails per second. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".","code":550}  
[2025-09-25 07:52:42] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 07:52:51] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-09-25 07:53:14] local.INFO: Domain deletion request email sent <NAME_EMAIL> for domain wallqueue.net  
[2025-09-25 07:53:52] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 07:53:56] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 07:54:13] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 07:54:20] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:00:41] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:02:33] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:02:43] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-09-25 08:05:32] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:08:04] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:18:57] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:19:00] local.INFO: Domain deletion request email sent <NAME_EMAIL> for domain hipocratey.com  
[2025-09-25 08:19:41] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:25:39] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:25:59] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:26:26] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:26:35] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:26:36] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:26:38] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:26:49] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:26:51] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:27:36] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:27:39] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:27:41] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:27:49] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:27:54] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:28:08] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-09-25 08:28:18] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-25 08:28:21] local.INFO: Domain deletion request email sent <NAME_EMAIL> for domain hipocratey.net  
[2025-09-25 08:29:07] local.INFO: Domain History: Domain deletion request cancelled by admin 1 (a@a.a)  
[2025-09-25 08:29:19] local.INFO: Domain deletion request email sent <NAME_EMAIL> for domain wallqueue.org  
[2025-09-25 08:30:20] local.INFO: Domain History: Domain deletion request rejected by admin 1 (a@a.a)  
[2025-09-25 08:30:36] local.INFO: Domain deletion request email sent <NAME_EMAIL> for domain portipor.org  
[2025-09-25 08:30:41] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-26 05:17:16] local.INFO: user login from 127.0.0.1  
[2025-09-26 05:37:09] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-09-26 05:37:15] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-09-26 05:37:16] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-09-29 05:56:45] local.INFO: user login from 127.0.0.1  
[2025-09-29 06:24:22] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-09-29 08:36:52] local.INFO: user login from 127.0.0.1  
[2025-09-29 08:40:40] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-29 08:41:08] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-29 08:42:20] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-29 08:43:03] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-29 08:43:50] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-30 02:07:57] local.INFO: user login from 127.0.0.1  
[2025-09-30 07:35:49] local.INFO: user login from 127.0.0.1  
[2025-10-01 07:24:04] local.INFO: user login from 127.0.0.1  
