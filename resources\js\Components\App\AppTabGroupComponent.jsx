//* PACKAGES 
import React from "react";

//* ICONS
//.. 

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* UTILS 
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function AppTabGroupComponent(
    {
        //! VALUES
        items, 
        
        //! STATES
        activeTab
        
        //! EVENTS
        //...
    }
)
{
    return (
        <div
            className="flex gap-5 flex-col"
        >
            <div
                className="border-blue-100 border-b-2"
            >
                <div
                    className="flex flex-col md:flex-row"
                >
                    {
                        items.labels.map(
                            (item, index) => 
                            {
                                return (
                                    <div
                                        key={index}
                                        className={`
                                            text-primary px-5 py-2 capitalize text-sm cursor-pointer ease-in duration-200 tracking-wide
                                            ${item.isActive ? 'bg-blue-50 font-bold' : 'font-semibold'}
                                        `}
                                        onClick={item.eventOnClick}
                                    >
                                        {item.value}
                                    </div>
                                );
                            }
                        )
                    }
                </div>
            </div>

            <div
                className=""
            >
                {
                    items.content.map(
                        (item, index) => 
                        {
                            return (
                                <div
                                    key={index}
                                    className={`
                                        ${item.isActive ? 'flex' : 'hidden'}
                                    `}
                                >
                                    {item.value}
                                </div>
                            );
                        }
                    )
                }
            </div>
        </div>
    );
}
